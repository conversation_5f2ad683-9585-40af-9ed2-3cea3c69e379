"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// Güvenli bir şekilde ana süreç ile iletişim kurmak için API
electron_1.contextBridge.exposeInMainWorld('electronAPI', {
    // Örnek: Ana süreçten veri almak
    getAppVersion () { return electron_1.ipcRenderer.invoke('get-app-version'); },
    // Örnek: Ana sürece veri göndermek
    printReceipt (data) { return electron_1.ipcRenderer.invoke('print-receipt', data); },
    // Örnek: <PERSON><PERSON> din<PERSON>
    onPrinterStatus (callback) {
        electron_1.ipcRenderer.on('printer-status', function (_event, status) { return callback(status); });
        return function () {
            electron_1.ipcRenderer.removeAllListeners('printer-status');
        };
    },
});
