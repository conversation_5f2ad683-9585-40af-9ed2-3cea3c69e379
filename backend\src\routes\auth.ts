import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { prisma } from '../index';
import bcrypt from 'bcryptjs';
import { z } from 'zod';

// Validation şemaları
const loginSchema = z.object({
  username: z.string().min(3),
  password: z.string().min(6),
});

export default async function (fastify: FastifyInstance) {
  // Login endpoint
  fastify.post('/login', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { username, password } = loginSchema.parse(request.body);

      // Kullanıcıyı bul
      const user = await prisma.user.findUnique({
        where: { username },
        include: {
          branch: true,
          company: true,
        },
      });

      if (!user || user.deletedAt || !user.active) {
        return reply.status(401).send({
          success: false,
          error: 'Unauthorized',
          message: '<PERSON><PERSON><PERSON><PERSON><PERSON> kullanıcı adı veya şifre',
        });
      }

      // <PERSON><PERSON><PERSON><PERSON> kontrol et
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        // Başarısız giriş sayısını artır
        await prisma.user.update({
          where: { id: user.id },
          data: {
            failedLoginCount: {
              increment: 1,
            },
          },
        });

        return reply.status(401).send({
          success: false,
          error: 'Unauthorized',
          message: 'Geçersiz kullanıcı adı veya şifre',
        });
      }

      // Başarılı giriş - sayacı sıfırla
      await prisma.user.update({
        where: { id: user.id },
        data: {
          failedLoginCount: 0,
          lastLoginAt: new Date(),
        },
      });

      // Oturum oluştur
      const session = await prisma.session.create({
        data: {
          userId: user.id,
          branchId: user.branchId,
          token: fastify.jwt.sign(
            {
              id: user.id,
              role: user.role,
              branchId: user.branchId,
              companyId: user.companyId,
            },
            { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
          ),
          deviceInfo: request.headers['user-agent'] || '',
          ipAddress: request.ip,
        },
      });

      // Kullanıcı bilgilerini döndür (şifre hariç)
      const { password: _password, pin: _pin, ...userWithoutPassword } = user;

      return reply.send({
        success: true,
        data: {
          user: userWithoutPassword,
          token: session.token,
        },
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(400).send({
        success: false,
        error: 'Bad Request',
        message: error instanceof z.ZodError ? error.errors : 'Geçersiz istek',
      });
    }
  });

  // Logout endpoint
  fastify.post(
    '/logout',
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const user = request.user;

        // Oturumu sonlandır
        await prisma.session.updateMany({
          where: {
            userId: user.id,
            token: request.headers.authorization?.replace('Bearer ', '') || '',
            endedAt: null,
          },
          data: {
            endedAt: new Date(),
          },
        });

        return reply.send({
          success: true,
          message: 'Başarıyla çıkış yapıldı',
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(500).send({
          success: false,
          error: 'Internal Server Error',
          message: 'Çıkış yapılırken bir hata oluştu',
        });
      }
    }
  );

  // Kullanıcı bilgilerini getir
  fastify.get(
    '/me',
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const user = request.user;

        const userData = await prisma.user.findUnique({
          where: { id: user.id },
          include: {
            branch: true,
            company: true,
          },
        });

        if (!userData || userData.deletedAt || !userData.active) {
          return reply.status(401).send({
            success: false,
            error: 'Unauthorized',
            message: 'Kullanıcı bulunamadı veya aktif değil',
          });
        }

        // Şifre ve pin bilgilerini çıkar
        const { password: _password, pin: _pin, ...userWithoutPassword } = userData;

        return reply.send({
          success: true,
          data: userWithoutPassword,
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(500).send({
          success: false,
          error: 'Internal Server Error',
          message: 'Kullanıcı bilgileri alınırken bir hata oluştu',
        });
      }
    }
  );
}
