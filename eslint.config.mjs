import globals from "globals";
import tseslint from "typescript-eslint";
import pluginReact from "eslint-plugin-react";
import reactHooks from "eslint-plugin-react-hooks";

export default [
  // Global ignores
  {
    ignores: [
      "**/dist/**",
      "**/dist-electron/**",
      "**/node_modules/**",
      "**/build/**",
      "**/*.config.js",
      "**/*.config.ts",
      "**/vite.config.ts"
    ]
  },

  // Electron files (main process)
  {
    files: ["frontend/electron/**/*.{js,ts}"],
    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.browser
      },
      parser: tseslint.parser,
      parserOptions: {
        ecmaVersion: 2020,
        sourceType: "module"
      }
    },
    plugins: {
      "@typescript-eslint": tseslint.plugin
    },
    rules: {
      // 🚨 KRİTİK KURALLAR
      "@typescript-eslint/no-unused-vars": "error",
      "no-unreachable": "error",
      "no-duplicate-imports": "error",

      // 🔥 ELECTRON SPECIFIC
      "no-console": "warn", // Electron'da console kullanımını sınırla
      "@typescript-eslint/no-explicit-any": "warn",

      // 🐛 BUG PREVENTION
      "no-undef": "error",
      "no-redeclare": "off", // Electron'da global'lar çakışabilir
      "no-var": "error",
      "prefer-const": "warn",
      "no-useless-catch": "warn",
      "no-empty": "warn"
    }
  },

  // Frontend files
  {
    files: ["frontend/**/*.{js,jsx,ts,tsx}"],
    ignores: ["frontend/electron/**"],
    languageOptions: {
      globals: {
        ...globals.browser,
        React: "readonly"
      },
      parser: tseslint.parser,
      parserOptions: {
        ecmaVersion: 2020,
        sourceType: "module",
        ecmaFeatures: { jsx: true }
      }
    },
    plugins: {
      "react-hooks": reactHooks,
      "react": pluginReact,
      "@typescript-eslint": tseslint.plugin
    },
    rules: {
      // 🚨 KRİTİK KURALLAR - ASLA İHLAL ETME!
      "react-hooks/rules-of-hooks": "error",
      "react-hooks/exhaustive-deps": "error", // warn → error
      "no-unreachable": "error",
      "no-duplicate-imports": "error", // Yeni eklendi

      // 🔥 ÖNEMLİ KURALLAR
      "@typescript-eslint/no-unused-vars": "error", // warn → error
      "@typescript-eslint/no-explicit-any": "warn", // off → warn (yavaş yavaş)
      "no-console": "warn", // off → warn (production'da kaldırılmalı)

      // 🐛 BUG PREVENTION
      "no-undef": "error",
      "no-redeclare": "off", // TypeScript const assertion pattern için disable
      "no-var": "error", // let/const kullan
      "prefer-const": "warn",

      // 🎯 REACT SPECIFIC
      "react/react-in-jsx-scope": "off", // React 17+
      "react/prop-types": "off", // TypeScript kullanıyoruz
      "react/jsx-key": "error", // Key prop zorunlu
      "react/jsx-no-duplicate-props": "error",
      "react/jsx-no-undef": "error",

      // 🚀 PERFORMANCE & BEST PRACTICES (Daha esnek)
      "react/jsx-no-bind": "off", // Çok fazla warning veriyor, kapat
      "no-useless-catch": "warn",
      "no-useless-escape": "warn",
      "no-empty": "warn",

      // 📝 CODE STYLE (Opsiyonel ama iyi)
      "prefer-template": "off", // Şimdilik kapat
      "object-shorthand": "off", // Şimdilik kapat
    }
  },

  // Backend files
  {
    files: ["backend/**/*.{js,ts}"],
    languageOptions: {
      globals: globals.node,
      parser: tseslint.parser,
      parserOptions: {
        ecmaVersion: 2020,
        sourceType: "module"
      }
    },
    plugins: {
      "@typescript-eslint": tseslint.plugin
    },
    rules: {
      // 🚨 KRİTİK KURALLAR
      "@typescript-eslint/no-unused-vars": "error", // warn → error
      "no-unreachable": "error",
      "no-duplicate-imports": "error",

      // 🔥 BACKEND SPECIFIC
      "no-console": "off", // Backend'de console.log normal
      "@typescript-eslint/no-explicit-any": "warn", // Yavaş yavaş azalt

      // 🐛 BUG PREVENTION
      "no-undef": "error",
      "no-redeclare": "off", // TypeScript const assertion pattern için disable
      "no-var": "error",
      "prefer-const": "warn",
      "no-useless-catch": "warn",
      "no-empty": "warn"
    }
  }
];
