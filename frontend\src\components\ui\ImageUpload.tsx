import React, { useState, useRef, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from './Button';
import { Card } from './Card';
import { ProductService } from '../../services/productService';

interface ImageUploadProps {
  value?: string;
  onChange: (imageUrl: string) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  className?: string;
  maxSize?: number; // MB cinsinden
  acceptedTypes?: string[];
}

export const ImageUpload: React.FC<ImageUploadProps> = ({
  value,
  onChange,
  onError,
  disabled = false,
  className = '',
  maxSize = 5,
  acceptedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
}) => {
  const { t } = useTranslation();
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [preview, setPreview] = useState<string | null>(value || null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Dosya validasyonu
  const validateFile = useCallback((file: File): { isValid: boolean; error?: string } => {
    // Dosya tipi kontrolü
    if (!acceptedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Sadece JPEG, PNG ve WebP formatları desteklenmektedir'
      };
    }

    // Dosya boyutu kontrolü
    const maxSizeBytes = maxSize * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return {
        isValid: false,
        error: `Dosya boyutu ${maxSize}MB'dan büyük olamaz`
      };
    }

    return { isValid: true };
  }, [acceptedTypes, maxSize]);

  // Dosya yükleme
  const uploadFile = useCallback(async (file: File) => {
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Progress simulation - gerçek progress tracking için XMLHttpRequest gerekli
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      const response = await ProductService.uploadProductImage(file);

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (response.success && response.data) {
        return response.data.imageUrl;
      } else {
        throw new Error(response.message || 'Dosya yüklenemedi');
      }

    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  }, []);

  // Dosya işleme
  const handleFile = useCallback(async (file: File) => {
    const validation = validateFile(file);
    if (!validation.isValid) {
      onError?.(validation.error!);
      return;
    }

    // Önizleme oluştur
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    try {
      const imageUrl = await uploadFile(file);
      onChange(imageUrl);
    } catch (error) {
      setPreview(null);
      onError?.(error instanceof Error ? error.message : 'Dosya yüklenemedi');
    }
  }, [validateFile, uploadFile, onChange, onError]);

  // Dosya seçme
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFile(file);
    }
  }, [handleFile]);

  // Drag & Drop
  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    if (!disabled) {
      setIsDragging(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);

    if (disabled) return;

    const file = event.dataTransfer.files[0];
    if (file) {
      handleFile(file);
    }
  }, [disabled, handleFile]);

  // Dosya seçme dialog'unu aç
  const openFileDialog = useCallback(() => {
    if (!disabled) {
      fileInputRef.current?.click();
    }
  }, [disabled]);

  // Resmi kaldır
  const removeImage = useCallback(() => {
    setPreview(null);
    onChange('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [onChange]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Dosya input (gizli) */}
      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedTypes.join(',')}
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
      />

      {/* Upload alanı */}
      <Card
        className={`
          relative border-2 border-dashed transition-colors cursor-pointer
          ${isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-gray-400'}
          ${preview ? 'p-2' : 'p-8'}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={preview ? undefined : openFileDialog}
      >
        {preview ? (
          // Önizleme
          <div className="relative">
            <img
              src={preview}
              alt="Önizleme"
              className="w-full h-48 object-cover rounded-lg"
            />
            {!disabled && (
              <div className="absolute top-2 right-2 space-x-2">
                <Button
                  type="button"
                  variant="secondary"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    openFileDialog();
                  }}
                  disabled={isUploading}
                >
                  {t('common.change')}
                </Button>
                <Button
                  type="button"
                  variant="danger"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    removeImage();
                  }}
                  disabled={isUploading}
                >
                  {t('common.remove')}
                </Button>
              </div>
            )}
          </div>
        ) : (
          // Upload alanı
          <div className="text-center">
            <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
              <svg fill="none" stroke="currentColor" viewBox="0 0 48 48">
                <path
                  d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                  strokeWidth={2}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <p className="text-sm text-gray-600 mb-2">
              {t('common.dragDropOrClick')}
            </p>
            <p className="text-xs text-gray-500">
              {acceptedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ')} - Max {maxSize}MB
            </p>
          </div>
        )}

        {/* Yükleme progress bar */}
        {isUploading && (
          <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center rounded-lg">
            <div className="text-center">
              <div className="w-32 bg-gray-200 rounded-full h-2 mb-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                />
              </div>
              <p className="text-sm text-gray-600">
                {t('common.uploading')} {uploadProgress}%
              </p>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};
