import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ProductService } from '../services/productService';
import { CategoryService } from '../services/categoryService';
import { TaxService } from '../services/taxService';
import type { ProductQueryParams, CreateProductRequest, UpdateProductRequest } from '../types/api';
import { useToast } from '../providers/ToastProvider';
import { useTranslation } from 'react-i18next';

// Query Keys
export const productKeys = {
  all: ['products'] as const,
  lists: () => [...productKeys.all, 'list'] as const,
  list: (params?: ProductQueryParams) => [...productKeys.lists(), params] as const,
  details: () => [...productKeys.all, 'detail'] as const,
  detail: (id: string) => [...productKeys.details(), id] as const,
  stats: () => [...productKeys.all, 'stats'] as const,
  popular: () => [...productKeys.all, 'popular'] as const,
  lowStock: () => [...productKeys.all, 'lowStock'] as const,
};

export const categoryKeys = {
  all: ['categories'] as const,
  lists: () => [...categoryKeys.all, 'list'] as const,
  hierarchy: () => [...categoryKeys.all, 'hierarchy'] as const,
  roots: () => [...categoryKeys.all, 'roots'] as const,
};

export const taxKeys = {
  all: ['taxes'] as const,
  lists: () => [...taxKeys.all, 'list'] as const,
  active: () => [...taxKeys.all, 'active'] as const,
  default: () => [...taxKeys.all, 'default'] as const,
};

// Products Hooks
export const useProducts = (params?: ProductQueryParams) => {
  return useQuery({
    queryKey: productKeys.list(params),
    queryFn: () => ProductService.getProducts(params),
    staleTime: 5 * 60 * 1000, // 5 dakika
    gcTime: 10 * 60 * 1000, // 10 dakika
  });
};

export const useProduct = (id: string) => {
  return useQuery({
    queryKey: productKeys.detail(id),
    queryFn: () => ProductService.getProduct(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

export const useProductStats = () => {
  return useQuery({
    queryKey: productKeys.stats(),
    queryFn: () => ProductService.getProductStats(),
    staleTime: 2 * 60 * 1000, // 2 dakika
  });
};

export const usePopularProducts = (limit?: number) => {
  return useQuery({
    queryKey: productKeys.popular(),
    queryFn: () => ProductService.getPopularProducts(limit),
    staleTime: 10 * 60 * 1000, // 10 dakika
  });
};

export const useLowStockProducts = () => {
  return useQuery({
    queryKey: productKeys.lowStock(),
    queryFn: () => ProductService.getLowStockProducts(),
    staleTime: 1 * 60 * 1000, // 1 dakika
  });
};

// Categories Hooks
export const useCategories = () => {
  return useQuery({
    queryKey: categoryKeys.lists(),
    queryFn: () => CategoryService.getCategories(),
    staleTime: 10 * 60 * 1000, // 10 dakika
  });
};

export const useCategoriesHierarchy = () => {
  return useQuery({
    queryKey: categoryKeys.hierarchy(),
    queryFn: () => CategoryService.getCategoriesHierarchy(),
    staleTime: 10 * 60 * 1000,
  });
};

export const useRootCategories = () => {
  return useQuery({
    queryKey: categoryKeys.roots(),
    queryFn: () => CategoryService.getRootCategories(),
    staleTime: 10 * 60 * 1000,
  });
};

// Taxes Hooks
export const useTaxes = () => {
  return useQuery({
    queryKey: taxKeys.lists(),
    queryFn: () => TaxService.getTaxes(),
    staleTime: 15 * 60 * 1000, // 15 dakika
  });
};

export const useActiveTaxes = () => {
  return useQuery({
    queryKey: taxKeys.active(),
    queryFn: () => TaxService.getTaxes({ active: true }),
    staleTime: 15 * 60 * 1000,
    select: (response) => response.data,
  });
};

export const useDefaultTax = () => {
  return useQuery({
    queryKey: taxKeys.default(),
    queryFn: () => TaxService.getDefaultTax(),
    staleTime: 15 * 60 * 1000,
  });
};

// Product Mutations
export const useCreateProduct = () => {
  const queryClient = useQueryClient();
  const { success, error } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (data: CreateProductRequest) => ProductService.createProduct(data),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: productKeys.all });
      queryClient.invalidateQueries({ queryKey: productKeys.stats() });
      success({
        title: t('products.productAdded'),
        message: response.message,
      });
    },
    onError: (err: any) => {
      error({
        title: t('common.error'),
        message: err.message || t('products.createError'),
      });
    },
  });
};

export const useUpdateProduct = () => {
  const queryClient = useQueryClient();
  const { success, error } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateProductRequest }) => 
      ProductService.updateProduct(id, data),
    onSuccess: (response, { id }) => {
      queryClient.invalidateQueries({ queryKey: productKeys.all });
      queryClient.invalidateQueries({ queryKey: productKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: productKeys.stats() });
      success({
        title: t('products.productUpdated'),
        message: response.message,
      });
    },
    onError: (err: any) => {
      error({
        title: t('common.error'),
        message: err.message || t('products.updateError'),
      });
    },
  });
};

export const useDeleteProduct = () => {
  const queryClient = useQueryClient();
  const { success, error } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (id: string) => ProductService.deleteProduct(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: productKeys.all });
      queryClient.invalidateQueries({ queryKey: productKeys.stats() });
      success({
        title: t('products.productDeleted'),
        message: response.message,
      });
    },
    onError: (err: any) => {
      error({
        title: t('common.error'),
        message: err.message || t('products.deleteError'),
      });
    },
  });
};

export const useToggleProductStatus = () => {
  const queryClient = useQueryClient();
  const { success, error } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: ({ id, active }: { id: string; active: boolean }) => ProductService.toggleProductStatus(id, active),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: productKeys.all });
      queryClient.invalidateQueries({ queryKey: productKeys.stats() });
      success({
        title: t('common.success'),
        message: t('products.statusUpdated'),
      });
    },
    onError: (err: any) => {
      error({
        title: t('common.error'),
        message: err.message || t('products.statusUpdateError'),
      });
    },
  });
};
