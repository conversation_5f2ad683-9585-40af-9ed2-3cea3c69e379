import React, { useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Select } from '../ui/Select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Modal } from '../ui/Modal';
import {
  categoryFormSchema,
  getDefaultCategoryFormValues,
  categoryToFormData,
  type CategoryFormData
} from '../../schemas/categorySchema';
import { 
  useCreateCategory, 
  useUpdateCategory, 
  useCategoriesHierarchy 
} from '../../hooks/useCategories';
import type { Category } from '../../types/api';

interface CategoryFormProps {
  isOpen: boolean;
  onClose: () => void;
  category?: Category | null;
  mode: 'create' | 'edit';
}

export const CategoryForm: React.FC<CategoryFormProps> = React.memo(({
  isOpen,
  onClose,
  category,
  mode,
}) => {
  const { t } = useTranslation();
  
  // Form setup
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(categoryFormSchema),
    defaultValues: getDefaultCategoryFormValues(),
  });

  // Queries
  const { data: rawCategories } = useCategoriesHierarchy();
  const categories: Category[] = Array.isArray(rawCategories) ? rawCategories : [];
  const createCategory = useCreateCategory();
  const updateCategory = useUpdateCategory();

  // Reset form when category changes
  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && category) {
        reset(categoryToFormData(category));
      } else {
        reset(getDefaultCategoryFormValues());
      }
    }
  }, [isOpen, mode, category, reset]);

  // Form submission
  const onSubmit = async (data: CategoryFormData) => {
    try {
      // Convert empty strings to undefined for optional fields
      const formData = {
        ...data,
        parentId: data.parentId || undefined,
        description: data.description || undefined,
        color: data.color || undefined,
        icon: data.icon || undefined,
        preparationTime: data.preparationTime || undefined,
      };

      if (mode === 'create') {
        await createCategory.mutateAsync(formData);
      } else if (category) {
        await updateCategory.mutateAsync({
          id: category.id,
          data: formData,
        });
      }
      
      onClose();
    } catch {
      // Error is handled by the mutation
    }
  };

  // Parent category options
  const parentCategoryOptions = [
    { value: '', label: t('categories.noParentCategory') },
    ...categories
      .filter((cat: Category) => mode === 'edit' ? cat.id !== category?.id : true) // Exclude self when editing
      .map((cat: Category) => ({
        value: cat.id,
        label: cat.parent ? `${cat.parent.name} > ${cat.name}` : cat.name,
      }))
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? t('categories.addCategory') : t('categories.editCategory')}
      size="lg"
      className="mx-4 max-h-[90vh] overflow-y-auto"
    >
      <form onSubmit={handleSubmit(onSubmit as any)} className="space-y-6">
        {/* Temel Bilgiler */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">{t('categories.basicInfo')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    label={t('categories.categoryName')}
                    error={errors.name?.message}
                    required
                  />
                )}
              />

              <Controller
                name="parentId"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    label={t('categories.parentCategory')}
                    options={parentCategoryOptions}
                    error={errors.parentId?.message}
                  />
                )}
              />
            </div>

            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  label={t('categories.description')}
                  error={errors.description?.message}
                />
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Controller
                name="color"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="color"
                    label={t('categories.categoryColor')}
                    error={errors.color?.message}
                  />
                )}
              />

              <Controller
                name="icon"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    label={t('categories.categoryIcon')}
                    error={errors.icon?.message}
                    placeholder="🍕"
                  />
                )}
              />
            </div>


          </CardContent>
        </Card>

        {/* Mutfak Ayarları */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">{t('categories.kitchenSettings')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Controller
                name="preparationTime"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    label={t('categories.preparationTime')}
                    error={errors.preparationTime?.message}
                    onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                  />
                )}
              />

              <div className="flex items-center space-x-2">
                <Controller
                  name="showInKitchen"
                  control={control}
                  render={({ field }) => (
                    <input
                      type="checkbox"
                      checked={field.value}
                      onChange={field.onChange}
                      className="rounded border-neutral-300 dark:border-neutral-600"
                    />
                  )}
                />
                <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                  {t('categories.showInKitchen')}
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Görüntüleme Ayarları */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">{t('categories.displaySettings')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Controller
                name="displayOrder"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    label={t('categories.displayOrder')}
                    error={errors.displayOrder?.message}
                    onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                  />
                )}
              />
            </div>

            <div className="flex flex-col space-y-2">
              <div className="flex items-center space-x-2">
                <Controller
                  name="showInMenu"
                  control={control}
                  render={({ field }) => (
                    <input
                      type="checkbox"
                      checked={field.value}
                      onChange={field.onChange}
                      className="rounded border-neutral-300 dark:border-neutral-600"
                    />
                  )}
                />
                <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                  {t('categories.showInMenu')}
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <Controller
                  name="active"
                  control={control}
                  render={({ field }) => (
                    <input
                      type="checkbox"
                      checked={field.value}
                      onChange={field.onChange}
                      className="rounded border-neutral-300 dark:border-neutral-600"
                    />
                  )}
                />
                <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                  {t('categories.active')}
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
          >
            {t('common.cancel')}
          </Button>
          <Button
            type="submit"
            loading={isSubmitting}
            disabled={isSubmitting}
          >
            {mode === 'create' ? t('common.create') : t('common.update')}
          </Button>
        </div>
      </form>
    </Modal>
  );
});
