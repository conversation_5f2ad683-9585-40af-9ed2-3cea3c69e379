import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ThemeMode, Language, THEME_MODES, LANGUAGES } from './types';

interface User {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  role: string;
  branchId: string | null;
  companyId: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (user: User, token: string) => void;
  logout: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      login: (user, token) => {
        set({ user, token, isAuthenticated: true });
      },
      logout: () => {
        set({ user: null, token: null, isAuthenticated: false });
      },
    }),
    {
      name: 'auth-storage',
    }
  )
);

interface SettingsState {
  theme: ThemeMode;
  language: Language;
  setTheme: (theme: ThemeMode) => void;
  setLanguage: (language: Language) => void;
}

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set) => ({
      theme: THEME_MODES.LIGHT,
      language: LANGUAGES.TR,
      setTheme: (theme) => set({ theme }),
      setLanguage: (language) => set({ language }),
    }),
    {
      name: 'settings-storage',
    }
  )
);

interface AppState {
  sidebarOpen: boolean;
  currentBranchId: string | null;
  setSidebarOpen: (open: boolean) => void;
  toggleSidebar: () => void;
  setCurrentBranchId: (branchId: string | null) => void;
}

export const useAppStore = create<AppState>()((set) => ({
  sidebarOpen: true,
  currentBranchId: null,
  setSidebarOpen: (open) => set({ sidebarOpen: open }),
  toggleSidebar: () => set((state) => ({ sidebarOpen: !state.sidebarOpen })),
  setCurrentBranchId: (branchId) => set({ currentBranchId: branchId }),
}));
