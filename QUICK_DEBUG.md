# 🚨 Hızlı Debug Rehberi

## 🔍 Sorun Tespit Sırası (30 saniye)

### 1. Console'a <PERSON>k
```bash
F12 → <PERSON>sole Tab
```
- ❌ Red errors var mı?
- ⚠️ Yellow warnings var mı?
- 🔍 Network errors var mı?

### 2. Network Tab'ı Kontrol Et
```bash
F12 → Network Tab → Reload
```
- 🔴 404/500 status codes
- 🔴 CORS errors
- 🔴 Failed requests

### 3. React DevTools
```bash
React DevTools → Components
```
- State doğru mu?
- Props geliyor mu?
- Re-render loop var mı?

## 🚀 Hızlı Çözümler

### API Çağrısı Çalışmıyor
```javascript
// 1. URL doğru mu kontrol et
console.log('API URL:', import.meta.env.VITE_API_URL)

// 2. Token var mı kontrol et
console.log('Token:', localStorage.getItem('auth-storage'))

// 3. Network tab'da request'i gör
```

### Resim Görünmüyor
```javascript
// 1. URL'yi console'da yazdır
console.log('Image URL:', imageUrl)

// 2. Browser'da direkt aç
// http://localhost:3000/uploads/...

// 3. CORS/Proxy kontrol et
```

### State Güncellenmiyor
```javascript
// 1. Mutation yapıyor musun?
const newState = [...oldState, newItem] // ✅ DOĞRU
oldState.push(newItem) // ❌ YANLIŞ

// 2. useEffect dependency eksik mi?
useEffect(() => {
  // logic
}, [dependency]) // ← Bu eksik mi?
```

### Component Render Olmuyor
```javascript
// 1. Key prop var mı?
{items.map(item => 
  <Item key={item.id} {...item} /> // ← key eksik mi?
)}

// 2. Conditional rendering doğru mu?
{condition && <Component />} // ← condition false mu?
```

## 🎯 5 Dakika Kuralı

**Eğer 5 dakikada çözemedin:**
1. 🔄 Sayfayı yenile
2. 🔄 Dev server'ı restart et
3. 🔄 Browser cache'i temizle
4. 📖 FRONTEND_RULES.md'yi oku
5. 🤝 Yardım iste

## 🚨 Acil Durum Komutları

```bash
# Frontend restart
cd frontend && npm run dev

# Backend restart  
cd backend && npm run dev

# Cache temizle
Ctrl + Shift + R

# Node modules temizle
rm -rf node_modules && npm install
```

---
**💡 Unutma:** En basit çözüm genellikle doğru çözümdür!
