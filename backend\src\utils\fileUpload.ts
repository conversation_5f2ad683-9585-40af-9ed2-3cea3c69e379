import { FastifyRequest } from 'fastify';
import { MultipartFile } from '@fastify/multipart';
import path from 'path';
import fs from 'fs/promises';
import { createWriteStream } from 'fs';
import { pipeline } from 'stream/promises';
import crypto from 'crypto';

// Desteklenen resim formatları
const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/webp'
];

// Maksimum dosya boyutu (5MB)
const MAX_FILE_SIZE = 5 * 1024 * 1024;

// Dosya adı oluşturma
export const generateFileName = (originalName: string): string => {
  const ext = path.extname(originalName);
  const timestamp = Date.now();
  const random = crypto.randomBytes(8).toString('hex');
  return `${timestamp}-${random}${ext}`;
};

// Dosya validasyonu
export const validateImageFile = (file: MultipartFile): { isValid: boolean; error?: string } => {
  // Dosya tipi kontrolü
  if (!ALLOWED_IMAGE_TYPES.includes(file.mimetype)) {
    return {
      isValid: false,
      error: 'Sadece JPEG, PNG ve WebP formatları desteklenmektedir'
    };
  }

  // Dosya boyutu kontrolü (eğer varsa)
  if (file.file.readableLength && file.file.readableLength > MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: 'Dosya boyutu 5MB\'dan büyük olamaz'
    };
  }

  return { isValid: true };
};

// Dosya yükleme
export const uploadProductImage = async (file: MultipartFile): Promise<{ success: boolean; filePath?: string; error?: string }> => {
  try {
    // Dosya validasyonu
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.error
      };
    }

    // Dosya adı oluştur
    const fileName = generateFileName(file.filename);
    const uploadDir = path.join(__dirname, '..', '..', 'uploads', 'products');
    const filePath = path.join(uploadDir, fileName);

    // Upload klasörünün var olduğundan emin ol
    await fs.mkdir(uploadDir, { recursive: true });

    // Dosyayı kaydet
    const writeStream = createWriteStream(filePath);
    await pipeline(file.file, writeStream);

    // Relatif path döndür (URL için)
    const relativePath = `/uploads/products/${fileName}`;

    return {
      success: true,
      filePath: relativePath
    };

  } catch (error) {
    console.error('Dosya yükleme hatası:', error);
    return {
      success: false,
      error: 'Dosya yüklenirken bir hata oluştu'
    };
  }
};

// Dosya silme
export const deleteProductImage = async (imagePath: string): Promise<{ success: boolean; error?: string }> => {
  try {
    if (!imagePath || imagePath === '') {
      return { success: true };
    }

    // Sadece uploads/products klasöründeki dosyaları sil
    if (!imagePath.startsWith('/uploads/products/')) {
      return { success: true }; // Harici URL'ler için işlem yapma
    }

    const fileName = path.basename(imagePath);
    const filePath = path.join(__dirname, '..', '..', 'uploads', 'products', fileName);

    // Dosyanın var olup olmadığını kontrol et
    try {
      await fs.access(filePath);
      await fs.unlink(filePath);
    } catch (error) {
      // Dosya zaten yoksa hata verme
      console.log('Silinecek dosya bulunamadı:', filePath);
    }

    return { success: true };

  } catch (error) {
    console.error('Dosya silme hatası:', error);
    return {
      success: false,
      error: 'Dosya silinirken bir hata oluştu'
    };
  }
};

// Multipart request'ten dosya alma
export const getFileFromRequest = async (request: FastifyRequest): Promise<{ file?: MultipartFile; error?: string }> => {
  try {
    const data = await request.file();
    
    if (!data) {
      return {
        error: 'Dosya bulunamadı'
      };
    }

    return { file: data };

  } catch (error) {
    console.error('Dosya alma hatası:', error);
    return {
      error: 'Dosya işlenirken bir hata oluştu'
    };
  }
};
